import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class DataController extends GetxController {
  RxString category = ''.obs;
  RxString userName = ''.obs;
  RxInt coins = 0.obs;
  RxInt totalScore = 0.obs;
  RxInt gamesPlayed = 0.obs;
  RxInt rank = 999.obs;
  RxDouble averageScore = 0.0.obs;

  final categoryNameController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    loadUserData();
  }

  void initCategory(String data) {
    category.value = data;
  }

  void initUsername(String data) {
    userName.value = data;
    saveUserData();
  }

  // Load user data from SharedPreferences
  Future<void> loadUserData() async {
    final prefs = await SharedPreferences.getInstance();
    userName.value = prefs.getString('username') ?? '';
    coins.value = prefs.getInt('coins') ?? 0;
    totalScore.value = prefs.getInt('totalScore') ?? 0;
    gamesPlayed.value = prefs.getInt('gamesPlayed') ?? 0;
    rank.value = prefs.getInt('rank') ?? 999;
    averageScore.value = prefs.getDouble('averageScore') ?? 0.0;
  }

  // Save user data to SharedPreferences
  Future<void> saveUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('username', userName.value);
    await prefs.setInt('coins', coins.value);
    await prefs.setInt('totalScore', totalScore.value);
    await prefs.setInt('gamesPlayed', gamesPlayed.value);
    await prefs.setInt('rank', rank.value);
    await prefs.setDouble('averageScore', averageScore.value);
  }

  // Update user stats after completing a quiz
  void updateQuizStats(int score, int totalQuestions) {
    totalScore.value += score;
    gamesPlayed.value += 1;

    // Award coins based on performance
    int earnedCoins = _calculateCoins(score, totalQuestions);
    coins.value += earnedCoins;

    // Calculate average score
    averageScore.value = totalScore.value / gamesPlayed.value;

    // Update rank based on total score
    _updateRank();

    // Save updated data
    saveUserData();
  }

  // Calculate coins earned based on quiz performance
  int _calculateCoins(int score, int totalQuestions) {
    double percentage = (score / totalQuestions) * 100;
    if (percentage >= 90) return 50;
    if (percentage >= 80) return 30;
    if (percentage >= 70) return 20;
    if (percentage >= 60) return 10;
    if (percentage >= 50) return 5;
    return 1; // Participation coin
  }

  // Update rank based on total score
  void _updateRank() {
    if (totalScore.value >= 1000) {
      rank.value = 1;
    } else if (totalScore.value >= 800) {
      rank.value = (1000 - totalScore.value) ~/ 10 + 1;
    } else if (totalScore.value >= 500) {
      rank.value = (800 - totalScore.value) ~/ 5 + 20;
    } else if (totalScore.value >= 200) {
      rank.value = (500 - totalScore.value) ~/ 3 + 80;
    } else {
      rank.value = 999 - totalScore.value;
    }

    // Ensure rank is at least 1
    if (rank.value < 1) rank.value = 1;
  }

  // Get user level based on total score
  int getUserLevel() {
    return (totalScore.value / 100).floor() + 1;
  }

  // Get progress to next level
  double getLevelProgress() {
    int currentLevelScore = (getUserLevel() - 1) * 100;
    int progressScore = totalScore.value - currentLevelScore;
    return progressScore / 100.0;
  }

  // Reset user data (for testing or new user)
  Future<void> resetUserData() async {
    userName.value = '';
    coins.value = 0;
    totalScore.value = 0;
    gamesPlayed.value = 0;
    rank.value = 999;
    averageScore.value = 0.0;
    await saveUserData();
  }

  // Future<void> showNamePopUp(BuildContext context) {
  //   return showDialog(
  //       context: context,
  //       builder: (context) {
  //         return AlertDialog(
  //             content: Column(
  //               mainAxisSize: MainAxisSize.min,
  //               children: [
  //                 TextField(
  //                   controller: categoryNameController,
  //                   keyboardType: TextInputType.text,
  //                   maxLines: 1,
  //                   decoration: const InputDecoration(
  //                       labelText: 'Enter your Name',
  //                       hintMaxLines: 1,
  //                       border: OutlineInputBorder(
  //                           borderSide:
  //                               BorderSide(color: Colors.green, width: 4.0))),
  //                 ),
  //                 const SizedBox(
  //                   height: 30.0,
  //                 ),
  //                 ElevatedButton(
  //                     onPressed: () {
  //                       userName.value = categoryNameController.text.trim();
  //                       Get.back();
  //                     },
  //                     child: const Text("Submit"))
  //               ],
  //             ),
  //             elevation: 12.0,
  //             shape: RoundedRectangleBorder(
  //                 borderRadius: BorderRadius.circular(10.0)));
  //       });
  // }
}
